package com.bkty.turtorsystem.controller;

import com.bkty.turtorsystem.entity.Orderinfo;
import com.bkty.turtorsystem.service.OrderinfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单信息控制器
 */
@RestController
@RequestMapping("/api/orderinfo")
@CrossOrigin(origins = {"http://localhost:8088", "http://127.0.0.1:8088"}, allowCredentials = "true")
public class OrderinfoController {

    private static final Logger logger = LoggerFactory.getLogger(OrderinfoController.class);

    @Autowired
    private OrderinfoService orderinfoService;

    /**
     * 创建订单
     * @param requestData 订单请求数据
     * @return 创建结果
     */
    @PostMapping("/add")
    public Map<String, Object> addOrder(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();

        try {
            logger.info("接收到创建订单原始请求数据: {}", requestData);

            // 从请求数据中提取字段，并创建Orderinfo对象
            Orderinfo orderinfo = new Orderinfo();

            // 设置订单ID
            String oid = (String) requestData.get("oid");
            if (oid == null || oid.isEmpty()) {
                oid = "DD" + System.currentTimeMillis(); // 生成新的订单ID
            }
            orderinfo.setOid(oid);

            // 设置家长账号
            String account = (String) requestData.get("account");
            if (account == null || account.isEmpty()) {
                result.put("code", 202);
                result.put("msg", "家长账号不能为空");
                return result;
            }
            orderinfo.setAccount(account);

            // 设置家教账号
            String taccount = (String) requestData.get("taccount");
            if (taccount == null || taccount.isEmpty()) {
                result.put("code", 203);
                result.put("msg", "家教账号不能为空");
                return result;
            }
            orderinfo.setTaccount(taccount);

            // 设置价格
            Object priceObj = requestData.get("price");
            if (priceObj == null) {
                result.put("code", 204);
                result.put("msg", "收费标准不能为空");
                return result;
            }

            Double price;
            if (priceObj instanceof Number) {
                price = ((Number) priceObj).doubleValue();
            } else if (priceObj instanceof String) {
                try {
                    price = Double.parseDouble((String) priceObj);
                } catch (NumberFormatException e) {
                    result.put("code", 204);
                    result.put("msg", "收费标准必须是有效的数字");
                    return result;
                }
            } else {
                result.put("code", 204);
                result.put("msg", "收费标准格式不正确");
                return result;
            }

            if (price <= 0) {
                result.put("code", 204);
                result.put("msg", "收费标准必须大于0");
                return result;
            }
            orderinfo.setPrice(price);

            // 设置课时
            Object hoursObj = requestData.get("hours");
            if (hoursObj == null) {
                result.put("code", 205);
                result.put("msg", "预约课时不能为空");
                return result;
            }

            Long hours;
            if (hoursObj instanceof Number) {
                hours = ((Number) hoursObj).longValue();
            } else if (hoursObj instanceof String) {
                try {
                    hours = Long.parseLong((String) hoursObj);
                } catch (NumberFormatException e) {
                    result.put("code", 205);
                    result.put("msg", "预约课时必须是有效的数字");
                    return result;
                }
            } else {
                result.put("code", 205);
                result.put("msg", "预约课时格式不正确");
                return result;
            }

            if (hours <= 0) {
                result.put("code", 205);
                result.put("msg", "预约课时必须大于0");
                return result;
            }
            orderinfo.setHours(hours);

            // 设置总金额
            Object amountObj = requestData.get("amount");
            Double amount;
            if (amountObj == null) {
                // 如果前端没有计算总金额，后端自动计算
                amount = price * hours;
            } else if (amountObj instanceof Number) {
                amount = ((Number) amountObj).doubleValue();
            } else if (amountObj instanceof String) {
                try {
                    amount = Double.parseDouble((String) amountObj);
                } catch (NumberFormatException e) {
                    // 如果转换失败，后端自动计算
                    amount = price * hours;
                }
            } else {
                // 如果格式不正确，后端自动计算
                amount = price * hours;
            }
            orderinfo.setAmount(amount);
            logger.info("设置总金额: {}", amount);

            // 设置备注
            String remarks = (String) requestData.get("remarks");
            orderinfo.setRemarks(remarks);

            // 设置状态
            String status = (String) requestData.get("status");
            if (status == null || status.isEmpty()) {
                status = "待接单";
            }
            orderinfo.setStatus(status);

            // 提交时间在服务层中设置，这里不需要处理

            logger.info("处理后的订单数据: {}", orderinfo);

            // 创建订单
            boolean success = orderinfoService.createOrder(orderinfo);

            if (success) {
                result.put("code", 200); // 统一使用200作为成功状态码
                result.put("msg", "创建订单成功");
                result.put("oid", orderinfo.getOid()); // 返回订单ID
                result.put("resdata", orderinfo); // 返回完整的订单对象
                logger.info("订单创建成功: {}", orderinfo.getOid());
            } else {
                result.put("code", 500);
                result.put("msg", "创建订单失败");
                logger.error("订单创建失败");
            }
        } catch (Exception e) {
            logger.error("创建订单异常", e);
            result.put("code", 500);
            result.put("msg", "系统错误: " + e.getMessage());
            // 打印详细的异常堆栈，便于调试
            e.printStackTrace();
        }

        logger.info("订单创建响应: {}", result);
        return result;
    }

    /**
     * 查询订单列表
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @param oid 订单编号（可选）
     * @param account 家长账号（可选）
     * @param taccount 家教账号（可选）
     * @param status 订单状态（可选）
     * @return 订单列表和分页信息
     */
    @RequestMapping(value = "/list", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> listOrders(
            @RequestParam(value = "currentPage", required = false) Integer currentPage,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "oid", required = false) String oid,
            @RequestParam(value = "account", required = false) String account,
            @RequestParam(value = "taccount", required = false) String taccount,
            @RequestParam(value = "status", required = false) String status) {

        logger.info("接收到查询订单列表请求: currentPage={}, pageSize={}, oid={}, account={}, taccount={}, status={}",
                currentPage, pageSize, oid, account, taccount, status);

        // 创建查询条件对象
        Orderinfo orderinfo = new Orderinfo();
        if (oid != null && !oid.isEmpty()) {
            orderinfo.setOid(oid);
        }
        if (account != null && !account.isEmpty()) {
            orderinfo.setAccount(account);
        }
        if (taccount != null && !taccount.isEmpty()) {
            orderinfo.setTaccount(taccount);
        }
        if (status != null && !status.isEmpty()) {
            orderinfo.setStatus(status);
        }

        // 调用服务层查询订单列表
        return orderinfoService.listOrders(orderinfo, currentPage, pageSize);
    }

    /**
     * 获取订单详情
     * @param oid 订单ID
     * @return 订单详情
     */
    @RequestMapping(value = "/get", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> getOrder(@RequestParam("id") String oid) {
        Map<String, Object> result = new HashMap<>();

        try {
            logger.info("接收到查询订单详情请求: oid={}", oid);

            // 参数校验
            if (oid == null || oid.isEmpty()) {
                result.put("code", 201);
                result.put("msg", "订单ID不能为空");
                return result;
            }

            // 查询订单
            Orderinfo orderinfo = orderinfoService.getOrderById(oid);

            if (orderinfo != null) {
                result.put("code", 0);
                result.put("msg", "查询成功");
                result.put("resdata", orderinfo);
            } else {
                result.put("code", 204);
                result.put("msg", "订单不存在");
            }
        } catch (Exception e) {
            logger.error("查询订单详情异常", e);
            result.put("code", 500);
            result.put("msg", "系统错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 更新订单状态
     * @param oid 订单ID
     * @param status 新状态
     * @return 更新结果
     */
    @RequestMapping(value = "/updateStatus", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> updateOrderStatus(
            @RequestParam(value = "id", required = false) String oid,
            @RequestParam(value = "status", required = false) String status,
            @RequestBody(required = false) Map<String, Object> requestBody) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 从请求体中获取参数（如果通过请求体传递）
            if (requestBody != null) {
                if (oid == null && requestBody.containsKey("id")) {
                    oid = (String) requestBody.get("id");
                }
                if (status == null && requestBody.containsKey("status")) {
                    status = (String) requestBody.get("status");
                }
            }

            logger.info("处理后的参数: oid={}, status={}", oid, status);

            // 参数校验
            if (oid == null || oid.isEmpty()) {
                result.put("code", 201);
                result.put("msg", "订单ID不能为空");
                return result;
            }

            if (status == null || status.isEmpty()) {
                result.put("code", 202);
                result.put("msg", "订单状态不能为空");
                return result;
            }

            // 更新订单状态
            boolean success = orderinfoService.updateOrderStatus(oid, status);

            if (success) {
                result.put("code", 0);
                result.put("msg", "更新订单状态成功");
            } else {
                result.put("code", 500);
                result.put("msg", "更新订单状态失败");
            }
        } catch (Exception e) {
            logger.error("更新订单状态异常", e);
            result.put("code", 500);
            result.put("msg", "系统错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 更新订单（兼容旧接口）
     * @param oid 订单ID
     * @param status 新状态
     * @return 更新结果
     */
    @RequestMapping(value = "/update", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> updateOrder(
            @RequestParam(value = "oid", required = false) String oid,
            @RequestParam(value = "id", required = false) String id,
            @RequestParam("status") String status) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 优先使用 oid 参数，如果没有则使用 id 参数
            String orderId = oid != null && !oid.isEmpty() ? oid : id;

            logger.info("接收到更新订单请求: orderId={}, status={}", orderId, status);

            // 参数校验
            if (orderId == null || orderId.isEmpty()) {
                result.put("code", 201);
                result.put("msg", "订单ID不能为空");
                return result;
            }

            if (status == null || status.isEmpty()) {
                result.put("code", 202);
                result.put("msg", "订单状态不能为空");
                return result;
            }

            // 更新订单状态
            boolean success = orderinfoService.updateOrderStatus(orderId, status);

            if (success) {
                result.put("code", 0);
                result.put("msg", "更新订单成功");
            } else {
                result.put("code", 500);
                result.put("msg", "更新订单失败");
            }
        } catch (Exception e) {
            logger.error("更新订单异常", e);
            result.put("code", 500);
            result.put("msg", "系统错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 删除订单
     * @param oid 订单ID
     * @return 删除结果
     */
    @RequestMapping(value = "/del", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> deleteOrder(@RequestParam("id") String oid) {
        Map<String, Object> result = new HashMap<>();

        try {
            logger.info("接收到删除订单请求: oid={}", oid);

            // 参数校验
            if (oid == null || oid.isEmpty()) {
                result.put("code", 201);
                result.put("msg", "订单ID不能为空");
                return result;
            }

            // 删除订单
            boolean success = orderinfoService.deleteOrder(oid);

            if (success) {
                result.put("code", 0);
                result.put("msg", "删除订单成功");
            } else {
                result.put("code", 500);
                result.put("msg", "删除订单失败");
            }
        } catch (Exception e) {
            logger.error("删除订单异常", e);
            result.put("code", 500);
            result.put("msg", "系统错误: " + e.getMessage());
        }

        return result;
    }
}